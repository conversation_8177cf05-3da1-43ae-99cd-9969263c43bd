export interface ProductLine {
  id: string;
  name: string;
  description?: string;
}

export interface Brand {
  id: string;
  name: string;
  country: string;
  description?: string;
  lines: ProductLine[];
}

export const professionalHairColorBrands: Brand[] = [
  // German Brands
  {
    id: 'wella',
    name: 'Wella Professionals',
    country: 'Germany',
    description: 'Leading professional hair color brand',
    lines: [
      {
        id: 'illumina',
        name: 'Illumina Color',
        description: 'Microlight technology for natural-looking color with luminous shine',
      },
      {
        id: 'koleston',
        name: 'Koleston Perfect',
        description: 'Permanent color with ME+ technology for reduced allergic reactions',
      },
      {
        id: 'color-touch',
        name: 'Color Touch',
        description: 'Demi-permanent color with up to 70% coverage and damage-free formula',
      },
      {
        id: 'blondor',
        name: '<PERSON>lon<PERSON>',
        description: 'Professional lightening system up to 9 levels with anti-yellow molecules',
      },
      {
        id: 'welloxon',
        name: 'Welloxon Perfect',
        description: 'Creamy developer in 6%, 9%, 12% for optimal color results',
      },
      {
        id: 'color-fresh',
        name: 'Color Fresh Create',
        description: 'Semi-permanent fashion colors lasting up to 20 washes',
      },
      {
        id: 'magma',
        name: 'Magma',
        description: 'Pigmented lightener for highlights without pre-lightening',
      },
      {
        id: 'shinefinity',
        name: 'Shinefinity',
        description: 'Zero lift, zero damage glazing service with balanced pH',
      },
    ],
  },
  {
    id: 'schwarzkopf',
    name: 'Schwarzkopf Professional',
    country: 'Germany',
    description: 'German engineering for hair color',
    lines: [
      {
        id: 'igora-royal',
        name: 'Igora Royal',
        description: 'High-performance permanent color',
      },
      {
        id: 'blondme',
        name: 'BlondMe',
        description: 'Premium lightening system',
      },
      {
        id: 'color10',
        name: 'Color10',
        description: '10-minute color service',
      },
      {
        id: 'igora-vibrance',
        name: 'Igora Vibrance',
        description: 'Demi-permanent color',
      },
      {
        id: 'igora-expert',
        name: 'Igora Expert Mousse',
        description: 'Toning mousse',
      },
      {
        id: 'fibreplex',
        name: 'Fibreplex',
        description: 'Bond connector technology',
      },
      {
        id: 'igora-zero',
        name: 'Igora Zero AMM',
        description: 'Ammonia-free color',
      },
      {
        id: 'igora-color-worx',
        name: 'Igora ColorWorx',
        description: 'Direct dye color',
      },
    ],
  },
  {
    id: 'goldwell',
    name: 'Goldwell',
    country: 'Germany',
    description: 'German precision in color',
    lines: [
      { id: 'topchic', name: 'Topchic', description: 'Permanent hair color' },
      {
        id: 'colorance',
        name: 'Colorance',
        description: 'Demi-permanent color',
      },
      { id: 'silk-lift', name: 'Silk Lift', description: 'Gentle lightening' },
      { id: 'elumen', name: 'Elumen', description: 'High-performance color' },
      { id: 'nectaya', name: 'Nectaya', description: 'Ammonia-free color' },
      { id: 'oxycur', name: 'Oxycur Platin', description: 'Lightening powder' },
      {
        id: 'colorance-ph68',
        name: 'Colorance pH 6.8',
        description: 'Acid color',
      },
    ],
  },
  {
    id: 'kadus',
    name: 'Kadus Professional',
    country: 'Germany',
    description: 'Creative color solutions',
    lines: [
      {
        id: 'kadus-color',
        name: 'Kadus Color',
        description: 'Permanent hair color',
      },
      {
        id: 'kadus-fervidol',
        name: 'Fervidol',
        description: 'Brilliant color',
      },
      {
        id: 'kadus-visible',
        name: 'Visible Repair',
        description: 'Reconstructive color',
      },
    ],
  },

  // French Brands
  {
    id: 'loreal',
    name: "L'Oréal Professionnel",
    country: 'France',
    description: 'Professional hair color innovation',
    lines: [
      {
        id: 'majirel',
        name: 'Majirel',
        description: 'Permanent color with Ionène G + Incell for 100% coverage and care',
      },
      {
        id: 'inoa',
        name: 'INOA',
        description: 'Oil Delivery System ammonia-free color with 60% oils',
      },
      {
        id: 'dia-light',
        name: 'Dia Light',
        description: 'Acidic gel-crème color pH 6.3 for shine and tone',
      },
      {
        id: 'dia-richesse',
        name: 'Dia Richesse',
        description: 'Alkaline demi-permanent for 70% white hair coverage',
      },
      {
        id: 'majirouge',
        name: 'Majirouge',
        description: 'Carmilane micro-pigments for intense, vibrant reds',
      },
      {
        id: 'majiblond',
        name: 'Majiblond Ultra',
        description: 'Up to 5 levels of lift with cool neutralization',
      },
      {
        id: 'smartbond',
        name: 'Smartbond',
        description: 'In-salon protective system for all color services',
      },
      {
        id: 'luocolor',
        name: 'LuoColor',
        description: 'Ammonia-free with luminous reflects and nutri-shine',
      },
      {
        id: 'majirel-cool-cover',
        name: 'Majirel Cool Cover',
        description: 'Cool brown shades with anti-red/orange technology',
      },
    ],
  },
  {
    id: 'eugene-perma',
    name: 'Eugène Perma',
    country: 'France',
    description: 'French professional hair care',
    lines: [
      { id: 'carmen', name: 'Carmen', description: 'Professional color line' },
      { id: 'solaris', name: 'Solaris', description: 'Lightening products' },
      { id: 'artiste', name: 'Artiste', description: 'Creative color' },
    ],
  },
  {
    id: 'phyto',
    name: 'Phyto Professional',
    country: 'France',
    description: 'Botanical hair color',
    lines: [
      {
        id: 'phytocolor',
        name: 'Phytocolor',
        description: 'Botanical permanent color',
      },
      {
        id: 'phytocolorbox',
        name: 'Phytocolor Box',
        description: 'Home color kit',
      },
    ],
  },

  // Italian Brands
  {
    id: 'alfaparf',
    name: 'Alfaparf Milano',
    country: 'Italy',
    description: 'Italian professional excellence',
    lines: [
      {
        id: 'evolution',
        name: 'Evolution of Color',
        description: 'Permanent hair color',
      },
      {
        id: 'revolution',
        name: 'Revolution',
        description: 'Ammonia-free color',
      },
      { id: 'yellow', name: 'Yellow', description: 'Fashion color line' },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Lightening system',
      },
      { id: 'color-wear', name: 'Color Wear', description: 'Direct dye color' },
      {
        id: 'precious-nature',
        name: 'Precious Nature',
        description: 'Natural ingredients color',
      },
    ],
  },
  {
    id: 'inebrya',
    name: 'Inebrya',
    country: 'Italy',
    description: 'Italian innovation in hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Permanent hair color' },
      { id: 'bionic', name: 'Bionic', description: 'Ammonia-free color' },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Lightening products',
      },
      {
        id: 'color-perfect',
        name: 'Color Perfect',
        description: 'Professional color line',
      },
    ],
  },
  {
    id: 'framesi',
    name: 'Framesi',
    country: 'Italy',
    description: 'Italian luxury hair color',
    lines: [
      {
        id: 'framcolor',
        name: 'FramColor',
        description: 'Permanent hair color',
      },
      {
        id: 'eclectic-care',
        name: 'Eclectic Care',
        description: 'Ammonia-free color',
      },
      { id: 'decolor-b', name: 'Decolor B', description: 'Lightening powder' },
      {
        id: 'hair-treatment',
        name: 'Hair Treatment',
        description: 'Color care system',
      },
    ],
  },
  {
    id: 'davines',
    name: 'Davines',
    country: 'Italy',
    description: 'Sustainable Italian hair color',
    lines: [
      { id: 'mask', name: 'Mask', description: 'Conditioning direct color' },
      {
        id: 'finest-pigments',
        name: 'Finest Pigments',
        description: 'Semi-permanent color',
      },
      {
        id: 'a-new-colour',
        name: 'A New Colour',
        description: 'Permanent color system',
      },
    ],
  },
  {
    id: 'kemon',
    name: 'Kemon',
    country: 'Italy',
    description: 'Italian professional hair color',
    lines: [
      { id: 'kroma', name: 'Kroma', description: 'Permanent color' },
      { id: 'nayo', name: 'Nayo', description: 'Ammonia-free color' },
      { id: 'lunex', name: 'Lunex', description: 'Lightening system' },
    ],
  },
  {
    id: 'selective',
    name: 'Selective Professional',
    country: 'Italy',
    description: 'Italian color innovation',
    lines: [
      { id: 'colorevo', name: 'ColorEvo', description: 'Permanent hair color' },
      { id: 'reverso', name: 'Reverso', description: 'Hair color remover' },
      { id: 'decolor', name: 'Decolor', description: 'Lightening powder' },
    ],
  },

  // Spanish Brands
  {
    id: 'salerm',
    name: 'Salerm Cosmetics',
    country: 'Spain',
    description: 'Spanish professional hair care',
    lines: [
      { id: 'vison', name: 'Vison', description: 'Permanent hair color' },
      {
        id: 'hi-repair',
        name: 'Hi Repair',
        description: 'Reconstructive treatment',
      },
      {
        id: 'technique',
        name: 'Technique',
        description: 'Styling and finishing',
      },
      {
        id: 'biokera',
        name: 'Biokera',
        description: 'Natural ingredients line',
      },
      {
        id: 'color-reverse',
        name: 'Color Reverse',
        description: 'Color remover',
      },
    ],
  },
  {
    id: 'lendan',
    name: 'Lendan Cosmetics',
    country: 'Spain',
    description: 'Spanish innovation in hair color',
    lines: [
      {
        id: 'color',
        name: 'Lendan Color',
        description: 'Professional permanent color',
      },
      {
        id: 'no-ammonia',
        name: 'No Ammonia',
        description: 'Ammonia-free color',
      },
      {
        id: 'semi-permanent',
        name: 'Semi Permanent',
        description: 'Gentle color option',
      },
      {
        id: 'bleaching',
        name: 'Bleaching',
        description: 'Lightening products',
      },
      { id: 'activator', name: 'Activator', description: 'Developer range' },
    ],
  },
  {
    id: 'tahe',
    name: 'Tahe Professional',
    country: 'Spain',
    description: 'Spanish professional hair care',
    lines: [
      {
        id: 'organic-care',
        name: 'Organic Care',
        description: 'Natural hair color',
      },
      { id: 'lumiere', name: 'Lumiere', description: 'Lightening system' },
      { id: 'magic', name: 'Magic', description: 'Color line' },
    ],
  },

  // American Brands
  {
    id: 'j-beverly-hills',
    name: 'J Beverly Hills',
    country: 'USA',
    description: 'Luxury professional hair color',
    lines: [
      {
        id: 'color',
        name: 'J Beverly Hills Color',
        description: 'Premium permanent color',
      },
      {
        id: 'platinum',
        name: 'Platinum',
        description: 'High-lift blonde series',
      },
      { id: 'clear', name: 'Clear', description: 'Color diluter and mixer' },
      { id: 'blue', name: 'Blue', description: 'Professional hair care' },
    ],
  },
  {
    id: 'matrix',
    name: 'Matrix',
    country: 'USA',
    description: 'Professional color innovation',
    lines: [
      {
        id: 'socolor',
        name: 'SoColor',
        description: 'Permanent creme hair color',
      },
      {
        id: 'colorsync',
        name: 'ColorSync',
        description: 'No-lift deposit-only color',
      },
      {
        id: 'light-master',
        name: 'Light Master',
        description: 'Lightening system',
      },
      {
        id: 'color-insider',
        name: 'Color Insider',
        description: 'Fashion-forward shades',
      },
      {
        id: 'socolor-cult',
        name: 'SoColor Cult',
        description: 'Direct dye color',
      },
      {
        id: 'socolor-beauty',
        name: 'SoColor Beauty',
        description: 'Ammonia-free color',
      },
    ],
  },
  {
    id: 'redken',
    name: 'Redken',
    country: 'USA',
    description: 'Science-based hair color',
    lines: [
      {
        id: 'shades-eq',
        name: 'Shades EQ',
        description: 'Acidic conditioning color',
      },
      {
        id: 'chromatics',
        name: 'Chromatics',
        description: 'Ultra-rich fashion color',
      },
      {
        id: 'color-extend',
        name: 'Color Extend',
        description: 'Color-protecting care',
      },
      {
        id: 'flash-lift',
        name: 'Flash Lift',
        description: 'Lightening powder',
      },
      {
        id: 'cover-fusion',
        name: 'Cover Fusion',
        description: 'Gray coverage',
      },
    ],
  },
  {
    id: 'joico',
    name: 'Joico',
    country: 'USA',
    description: 'Quadramine complex technology',
    lines: [
      {
        id: 'lumishine',
        name: 'LumiShine',
        description: 'Demi-permanent liquid color',
      },
      {
        id: 'vero-k-pak',
        name: 'Vero K-PAK',
        description: 'Permanent creme color',
      },
      {
        id: 'blonde-life',
        name: 'Blonde Life',
        description: 'Lightening system',
      },
      {
        id: 'color-intensity',
        name: 'Color Intensity',
        description: 'Semi-permanent color',
      },
      { id: 'instamat', name: 'InstaMat', description: 'Toning system' },
    ],
  },
  {
    id: 'aveda',
    name: 'Aveda',
    country: 'USA',
    description: 'Plant-based professional color',
    lines: [
      {
        id: 'full-spectrum',
        name: 'Full Spectrum',
        description: 'Permanent hair color',
      },
      {
        id: 'enlightener',
        name: 'Enlightener',
        description: 'Lightening powder',
      },
      { id: 'demi-plus', name: 'Demi+', description: 'Demi-permanent color' },
    ],
  },
  {
    id: 'paul-mitchell',
    name: 'Paul Mitchell',
    country: 'USA',
    description: 'Cruelty-free professional color',
    lines: [
      {
        id: 'the-color',
        name: 'The Color',
        description: 'Permanent hair color',
      },
      { id: 'shines', name: 'Shines', description: 'Demi-permanent color' },
      {
        id: 'lighten-up',
        name: 'Lighten Up',
        description: 'Lightening products',
      },
      { id: 'inkworks', name: 'Inkworks', description: 'Gray coverage color' },
      { id: 'pop-xg', name: 'Pop XG', description: 'Fashion color' },
    ],
  },
  {
    id: 'pravana',
    name: 'Pravana',
    country: 'USA',
    description: 'Vivid fashion color specialists',
    lines: [
      {
        id: 'chromasilk',
        name: 'ChromaSilk',
        description: 'Vivid fashion colors',
      },
      {
        id: 'pure-light',
        name: 'Pure Light',
        description: 'Lightening system',
      },
      {
        id: 'express-tones',
        name: 'Express Tones',
        description: 'Quick toning colors',
      },
      { id: 'vivids', name: 'Vivids', description: 'Direct dye colors' },
    ],
  },
  {
    id: 'revlon',
    name: 'Revlon Professional',
    country: 'USA',
    description: 'Professional color expertise',
    lines: [
      {
        id: 'revlonissimo',
        name: 'Revlonissimo',
        description: 'High-performance color',
      },
      {
        id: 'young-color-excel',
        name: 'Young Color Excel',
        description: 'Ammonia-free color',
      },
      {
        id: 'nutri-color',
        name: 'Nutri Color',
        description: 'Conditioning color',
      },
      {
        id: 'colorsmetique',
        name: 'Colorsmetique',
        description: 'Permanent color',
      },
    ],
  },
  {
    id: 'clairol',
    name: 'Clairol Professional',
    country: 'USA',
    description: 'Professional color innovation',
    lines: [
      {
        id: 'premium-creme',
        name: 'Premium Creme',
        description: 'Permanent hair color',
      },
      { id: 'jazzing', name: 'Jazzing', description: 'Temporary hair color' },
      { id: 'bw2', name: 'BW2', description: 'Lightening powder' },
      { id: 'soy4plex', name: 'Soy4Plex', description: 'Conditioning color' },
    ],
  },
  {
    id: 'kenra',
    name: 'Kenra Professional',
    country: 'USA',
    description: 'Professional hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Permanent hair color' },
      { id: 'demi', name: 'Demi', description: 'Demi-permanent color' },
      { id: 'lightener', name: 'Lightener', description: 'Bleaching products' },
    ],
  },

  // Japanese Brands
  {
    id: 'milbon',
    name: 'Milbon',
    country: 'Japan',
    description: 'Japanese hair color technology',
    lines: [
      { id: 'ordeve', name: 'Ordeve', description: 'Permanent hair color' },
      { id: 'addicthy', name: 'Addicthy', description: 'Fashion color' },
      { id: 'deesses', name: 'Deesses', description: 'Hair care line' },
    ],
  },
  {
    id: 'lebel',
    name: 'Lebel Cosmetics',
    country: 'Japan',
    description: 'Japanese professional hair care',
    lines: [
      { id: 'materia', name: 'Materia', description: 'Hair color line' },
      { id: 'luquias', name: 'Luquias', description: 'Premium color' },
      { id: 'theo', name: 'Theo', description: 'Scalp care color' },
    ],
  },
  {
    id: 'shiseido',
    name: 'Shiseido Professional',
    country: 'Japan',
    description: 'Japanese beauty innovation',
    lines: [
      { id: 'primience', name: 'Primience', description: 'Hair color system' },
      {
        id: 'crystallizing',
        name: 'Crystallizing',
        description: 'Straightening color',
      },
    ],
  },

  // Dutch Brands
  {
    id: 'keune',
    name: 'Keune',
    country: 'Netherlands',
    description: 'Dutch professional hair color',
    lines: [
      {
        id: 'tinta-color',
        name: 'Tinta Color',
        description: 'Permanent hair color',
      },
      {
        id: 'semi-color',
        name: 'Semi Color',
        description: 'Demi-permanent color',
      },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Lightening products',
      },
      { id: 'so-pure', name: 'So Pure', description: 'Natural color line' },
    ],
  },

  // British Brands
  {
    id: 'tigi',
    name: 'TIGI Professional',
    country: 'United Kingdom',
    description: 'British creative hair color',
    lines: [
      { id: 'creative', name: 'Creative', description: 'Fashion color line' },
      { id: 'colour', name: 'Colour', description: 'Permanent hair color' },
      { id: 'blonde', name: 'Blonde', description: 'Lightening system' },
    ],
  },
  {
    id: 'wella-uk',
    name: 'Wella UK',
    country: 'United Kingdom',
    description: 'British Wella division',
    lines: [
      {
        id: 'professionals',
        name: 'Professionals',
        description: 'UK professional line',
      },
    ],
  },

  // Australian Brands
  {
    id: 'kevin-murphy',
    name: 'Kevin Murphy',
    country: 'Australia',
    description: 'Australian luxury hair color',
    lines: [
      { id: 'color-me', name: 'Color.Me', description: 'Fashion color line' },
      {
        id: 'blonde-angel',
        name: 'Blonde.Angel',
        description: 'Lightening treatment',
      },
    ],
  },

  // Canadian Brands
  {
    id: 'schwarzkopf-canada',
    name: 'Schwarzkopf Canada',
    country: 'Canada',
    description: 'Canadian professional division',
    lines: [{ id: 'igora-ca', name: 'Igora CA', description: 'Canadian color line' }],
  },

  // Brazilian Brands
  {
    id: 'amend',
    name: 'Amend',
    country: 'Brazil',
    description: 'Brazilian professional hair care',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      { id: 'expertise', name: 'Expertise', description: 'Premium color' },
    ],
  },
  {
    id: 'felps',
    name: 'Felps Professional',
    country: 'Brazil',
    description: 'Brazilian hair color innovation',
    lines: [
      { id: 'color', name: 'Color', description: 'Permanent hair color' },
      { id: 'omega', name: 'Omega', description: 'Hair treatment color' },
    ],
  },

  // Korean Brands
  {
    id: 'mise-en-scene',
    name: 'Mise En Scene',
    country: 'South Korea',
    description: 'Korean professional hair color',
    lines: [
      {
        id: 'hello-bubble',
        name: 'Hello Bubble',
        description: 'Foam hair color',
      },
      { id: 'perfect', name: 'Perfect', description: 'Professional color' },
    ],
  },

  // Russian Brands
  {
    id: 'estel',
    name: 'Estel Professional',
    country: 'Russia',
    description: 'Russian professional hair color',
    lines: [
      { id: 'de-luxe', name: 'De Luxe', description: 'Premium color line' },
      { id: 'essex', name: 'Essex', description: 'Professional color' },
      {
        id: 'princess',
        name: 'Princess Essex',
        description: 'Ammonia-free color',
      },
      {
        id: 'haute-couture',
        name: 'Haute Couture',
        description: 'Fashion color',
      },
    ],
  },
  {
    id: 'kapous',
    name: 'Kapous Professional',
    country: 'Russia',
    description: 'Russian hair color brand',
    lines: [
      {
        id: 'hyaluronic',
        name: 'Hyaluronic',
        description: 'Hyaluronic acid color',
      },
      {
        id: 'magic-keratin',
        name: 'Magic Keratin',
        description: 'Keratin color',
      },
      {
        id: 'non-ammonia',
        name: 'Non Ammonia',
        description: 'Ammonia-free color',
      },
    ],
  },

  // Polish Brands
  {
    id: 'indola',
    name: 'Indola',
    country: 'Poland',
    description: 'Polish professional hair color',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent Caring Color',
        description: 'Caring permanent color',
      },
      { id: 'rapid', name: 'Rapid Blond', description: 'Fast lightening' },
      {
        id: 'profession',
        name: 'Profession',
        description: 'Professional color line',
      },
    ],
  },

  // Czech Brands
  {
    id: 'subrina',
    name: 'Subrina Professional',
    country: 'Czech Republic',
    description: 'Czech professional hair color',
    lines: [
      { id: 'unique', name: 'Unique', description: 'Professional color line' },
      { id: 'mad-touch', name: 'Mad Touch', description: 'Fashion color' },
    ],
  },

  // Swedish Brands
  {
    id: 'maria-nila',
    name: 'Maria Nila',
    country: 'Sweden',
    description: 'Swedish sustainable hair color',
    lines: [
      {
        id: 'colour-refresh',
        name: 'Colour Refresh',
        description: 'Color depositing mask',
      },
      { id: 'pure-color', name: 'Pure Color', description: 'Vegan hair color' },
    ],
  },

  // Norwegian Brands
  {
    id: 'cutrin',
    name: 'Cutrin',
    country: 'Norway',
    description: 'Nordic professional hair color',
    lines: [
      { id: 'aurora', name: 'Aurora', description: 'Permanent color' },
      {
        id: 'reflection',
        name: 'Reflection',
        description: 'Demi-permanent color',
      },
    ],
  },

  // Additional International Brands
  {
    id: 'fanola',
    name: 'Fanola',
    country: 'Italy',
    description: 'Italian professional hair care',
    lines: [
      { id: 'color', name: 'Color', description: 'Permanent hair color' },
      {
        id: 'no-yellow',
        name: 'No Yellow',
        description: 'Anti-yellow shampoo',
      },
      {
        id: 'botugen',
        name: 'Botugen',
        description: 'Reconstructive treatment',
      },
    ],
  },
  {
    id: 'lisap',
    name: 'Lisap Milano',
    country: 'Italy',
    description: 'Italian hair color innovation',
    lines: [
      { id: 'lisaplex', name: 'LisapLex', description: 'Bond builder system' },
      {
        id: 'easy-absolute',
        name: 'Easy Absolute',
        description: 'Ammonia-free color',
      },
      { id: 'splendor', name: 'Splendor', description: 'Permanent color' },
    ],
  },
  {
    id: 'bbcos',
    name: 'BBCos',
    country: 'Italy',
    description: 'Italian professional hair color',
    lines: [
      {
        id: 'keratin',
        name: 'Keratin Color',
        description: 'Keratin-enriched color',
      },
      {
        id: 'innovation',
        name: 'Innovation Evo',
        description: 'Advanced color technology',
      },
    ],
  },
  {
    id: 'farmavita',
    name: 'Farmavita',
    country: 'Italy',
    description: 'Italian hair color expertise',
    lines: [
      {
        id: 'life-color',
        name: 'Life Color Plus',
        description: 'Permanent hair color',
      },
      { id: 'bleach', name: 'Bleach', description: 'Lightening powder' },
    ],
  },
  {
    id: 'vitality',
    name: "Vitality's",
    country: 'Italy',
    description: 'Italian natural hair color',
    lines: [
      {
        id: 'tone-intense',
        name: 'Tone Intense',
        description: 'Intensive color',
      },
      { id: 'art', name: 'Art', description: 'Creative color line' },
    ],
  },
  {
    id: 'echosline',
    name: 'Echosline',
    country: 'Italy',
    description: 'Italian professional hair care',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      { id: 'fashion', name: 'Fashion Color', description: 'Trend colors' },
    ],
  },
  {
    id: 'green-light',
    name: 'Green Light',
    country: 'Italy',
    description: 'Italian eco-friendly hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Eco-friendly color' },
      { id: 'keratin', name: 'Keratin', description: 'Keratin color line' },
    ],
  },
  {
    id: 'hair-company',
    name: 'Hair Company',
    country: 'Italy',
    description: 'Italian hair color innovation',
    lines: [
      {
        id: 'inimitable',
        name: 'Inimitable',
        description: 'Premium color line',
      },
      {
        id: 'professional',
        name: 'Professional',
        description: 'Professional color',
      },
    ],
  },
  {
    id: 'oyster',
    name: 'Oyster Cosmetics',
    country: 'Italy',
    description: 'Italian professional hair color',
    lines: [
      {
        id: 'perlacolor',
        name: 'Perlacolor',
        description: 'Pearl-enriched color',
      },
      {
        id: 'perlaplus',
        name: 'Perlaplus',
        description: 'Advanced color system',
      },
    ],
  },
  {
    id: 'dikson',
    name: 'Dikson',
    country: 'Italy',
    description: 'Italian hair color tradition',
    lines: [
      { id: 'color', name: 'Color', description: 'Traditional color line' },
      { id: 'drop-color', name: 'Drop Color', description: 'Liquid color' },
    ],
  },
  {
    id: 'bionike',
    name: 'BioNike',
    country: 'Italy',
    description: 'Italian dermatological hair color',
    lines: [
      { id: 'shine-on', name: 'Shine On', description: 'Gentle hair color' },
      { id: 'defence', name: 'Defence', description: 'Sensitive scalp color' },
    ],
  },
  {
    id: 'cotril',
    name: 'Cotril',
    country: 'Italy',
    description: 'Italian professional hair care',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      { id: 'creative', name: 'Creative Walk', description: 'Fashion color' },
    ],
  },
  {
    id: 'maxima',
    name: 'Maxima',
    country: 'Italy',
    description: 'Italian hair color solutions',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color' },
      { id: 'bleach', name: 'Bleach', description: 'Lightening system' },
    ],
  },
  {
    id: 'nouvelle',
    name: 'Nouvelle',
    country: 'Italy',
    description: 'Italian color innovation',
    lines: [
      {
        id: 'hair-color',
        name: 'Hair Color',
        description: 'Professional color line',
      },
      { id: 'touch', name: 'Touch', description: 'Quick color touch-up' },
    ],
  },
  {
    id: 'periche',
    name: 'Periche Professional',
    country: 'Spain',
    description: 'Spanish professional hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Permanent hair color' },
      {
        id: 'cybercolor',
        name: 'Cybercolor',
        description: 'Advanced color technology',
      },
    ],
  },
  {
    id: 'montibello',
    name: 'Montibello',
    country: 'Spain',
    description: 'Spanish hair color expertise',
    lines: [
      { id: 'cromatone', name: 'Cromatone', description: 'Professional color' },
      { id: 'oalia', name: 'Oalia', description: 'Ammonia-free color' },
    ],
  },
  {
    id: 'kativa',
    name: 'Kativa',
    country: 'Spain',
    description: 'Spanish natural hair care',
    lines: [
      {
        id: 'keratin',
        name: 'Keratin',
        description: 'Keratin color treatment',
      },
      { id: 'collagen', name: 'Collagen', description: 'Anti-aging color' },
    ],
  },
  {
    id: 'exitenn',
    name: 'Exitenn',
    country: 'Spain',
    description: 'Spanish professional hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      { id: 'fashion', name: 'Fashion Color', description: 'Trend colors' },
    ],
  },
  {
    id: 'nirvel',
    name: 'Nirvel Professional',
    country: 'Spain',
    description: 'Spanish hair color innovation',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color' },
      { id: 'artx', name: 'ArtX', description: 'Creative color line' },
    ],
  },
  {
    id: 'postquam',
    name: 'Postquam Professional',
    country: 'Spain',
    description: 'Spanish professional hair care',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      {
        id: 'keratin',
        name: 'Keratin',
        description: 'Keratin color treatment',
      },
    ],
  },
  {
    id: 'eugene-color',
    name: 'Eugène Color',
    country: 'France',
    description: 'French color expertise',
    lines: [
      {
        id: 'professional',
        name: 'Professional',
        description: 'Professional color line',
      },
      { id: 'fashion', name: 'Fashion', description: 'Fashion color' },
    ],
  },
  {
    id: 'subtil',
    name: 'Subtil',
    country: 'France',
    description: 'French professional hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color' },
      { id: 'blonde', name: 'Blonde', description: 'Lightening system' },
    ],
  },
  {
    id: 'ducastel',
    name: 'Ducastel Subtil',
    country: 'France',
    description: 'French hair color tradition',
    lines: [
      { id: 'subtil', name: 'Subtil', description: 'Traditional color line' },
      { id: 'design', name: 'Design', description: 'Creative color' },
    ],
  },
  {
    id: 'schwarzkopf-igora',
    name: 'Schwarzkopf Igora',
    country: 'Germany',
    description: 'German Igora specialist line',
    lines: [
      {
        id: 'royal-absolutes',
        name: 'Royal Absolutes',
        description: 'Mature hair color',
      },
      {
        id: 'royal-disheveled',
        name: 'Royal Disheveled',
        description: 'Fashion color',
      },
    ],
  },
  {
    id: 'lanza',
    name: 'Lanza',
    country: 'USA',
    description: 'American healing hair color',
    lines: [
      { id: 'healing', name: 'Healing', description: 'Healing color system' },
      {
        id: 'trauma',
        name: 'Trauma Treatment',
        description: 'Reconstructive color',
      },
    ],
  },
  {
    id: 'rusk',
    name: 'Rusk',
    country: 'USA',
    description: 'American professional hair color',
    lines: [
      {
        id: 'deepshine',
        name: 'Deepshine',
        description: 'Color-enhancing system',
      },
      { id: 'color', name: 'Color', description: 'Professional color line' },
    ],
  },
  {
    id: 'chi',
    name: 'CHI',
    country: 'USA',
    description: 'American ionic hair color',
    lines: [
      { id: 'ionic', name: 'Ionic', description: 'Ionic color system' },
      { id: 'color', name: 'Color', description: 'Professional color' },
    ],
  },
  {
    id: 'sebastian',
    name: 'Sebastian Professional',
    country: 'USA',
    description: 'American creative hair color',
    lines: [
      {
        id: 'cellophanes',
        name: 'Cellophanes',
        description: 'Shine color treatment',
      },
      { id: 'color', name: 'Color', description: 'Professional color line' },
    ],
  },
  {
    id: 'tigi-bed-head',
    name: 'TIGI Bed Head',
    country: 'United Kingdom',
    description: 'British creative color',
    lines: [
      { id: 'colour', name: 'Colour', description: 'Creative color line' },
      {
        id: 'dumb-blonde',
        name: 'Dumb Blonde',
        description: 'Blonde care system',
      },
    ],
  },
  {
    id: 'osmo',
    name: 'Osmo',
    country: 'United Kingdom',
    description: 'British professional hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color' },
      { id: 'ikon', name: 'Ikon', description: 'Fashion color line' },
    ],
  },

  // Additional American Brands
  {
    id: 'ion',
    name: 'Ion',
    country: 'USA',
    description: 'Professional vibrant colors at Sally Beauty',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent Brights',
        description: 'Vibrant permanent hair color',
      },
      {
        id: 'demi',
        name: 'Demi Permanent',
        description: 'Long-lasting demi-permanent color',
      },
      {
        id: 'brilliance',
        name: 'Color Brilliance',
        description: 'Brilliant shine formula',
      },
      {
        id: 'intensive',
        name: 'Intensive Shine',
        description: 'High-gloss color system',
      },
      {
        id: 'liquid',
        name: 'Liquid Hair Color',
        description: 'Easy-application liquid formula',
      },
    ],
  },
  {
    id: 'arctic-fox',
    name: 'Arctic Fox',
    country: 'USA',
    description: 'Semi-permanent vegan hair color',
    lines: [
      {
        id: 'semi-permanent',
        name: 'Semi-Permanent',
        description: 'Vegan & cruelty-free colors',
      },
      {
        id: 'diluter',
        name: 'Diluter',
        description: 'Mix to create pastel shades',
      },
      {
        id: 'virgin-pink',
        name: 'Virgin Pink Collection',
        description: 'Pink tone variations',
      },
      {
        id: 'aquamarine',
        name: 'Aquamarine Collection',
        description: 'Blue-green shades',
      },
    ],
  },
  {
    id: 'madison-reed',
    name: 'Madison Reed',
    country: 'USA',
    description: 'Professional-grade ammonia-free color',
    lines: [
      {
        id: 'radiant',
        name: 'Radiant Hair Color',
        description: 'Ammonia-free permanent color',
      },
      {
        id: 'root-touch-up',
        name: 'Root Touch Up',
        description: 'Quick root coverage',
      },
      {
        id: 'color-reviving',
        name: 'Color Reviving Gloss',
        description: 'Semi-permanent gloss treatment',
      },
    ],
  },
  {
    id: 'igk',
    name: 'IGK',
    country: 'USA',
    description: 'Modern professional hair color',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent Color Kit',
        description: 'Salon-quality permanent color',
      },
      { id: 'foamo', name: 'Foamo', description: 'Foam-based color system' },
      {
        id: 'direct-dye',
        name: 'Direct Dye',
        description: 'Vibrant temporary colors',
      },
    ],
  },

  // Additional Brazilian Brands
  {
    id: 'cadiveu',
    name: 'Cadiveu Professional',
    country: 'Brazil',
    description: 'Brazilian professional hair solutions',
    lines: [
      {
        id: 'buriti',
        name: 'Buriti Mechas',
        description: 'Highlighting system with buriti oil',
      },
      {
        id: 'superclear',
        name: 'Superclear',
        description: 'High-lift lightening powder',
      },
      {
        id: 'color',
        name: 'Professional Color',
        description: 'Permanent color line',
      },
    ],
  },
  {
    id: 'truss',
    name: 'Truss Professional',
    country: 'Brazil',
    description: 'High-performance Brazilian hair color',
    lines: [
      {
        id: 'color',
        name: 'Professional Color',
        description: 'Advanced color technology',
      },
      {
        id: 'blond',
        name: 'Specific Blond',
        description: 'Specialized blonde treatments',
      },
      {
        id: 'nano',
        name: 'Nano Regeneration',
        description: 'Color with regenerative treatment',
      },
    ],
  },

  // Mexican Brands
  {
    id: 'recamier',
    name: 'Recamier Professional',
    country: 'Mexico',
    description: 'Mexican professional hair care',
    lines: [
      {
        id: 'saloon-in',
        name: 'SaloonIn',
        description: 'Professional permanent color',
      },
      {
        id: 'keratina',
        name: 'Keratina Color',
        description: 'Keratin-infused color',
      },
      {
        id: 'argan',
        name: 'Argan Color',
        description: 'Argan oil enriched color',
      },
    ],
  },
  {
    id: 'issue',
    name: 'Issue Professional',
    country: 'Mexico',
    description: 'Latin American professional color',
    lines: [
      {
        id: 'colorissue',
        name: 'Colorissue',
        description: 'Permanent hair color',
      },
      { id: 'deco', name: 'Deco', description: 'Lightening products' },
      {
        id: 'fantasy',
        name: 'Fantasy Colors',
        description: 'Fashion color collection',
      },
    ],
  },

  // Argentinian Brands
  {
    id: 'fidelite',
    name: 'Fidelité',
    country: 'Argentina',
    description: 'Argentinian professional hair color',
    lines: [
      {
        id: 'coloracion',
        name: 'Coloración Permanente',
        description: 'Permanent color system',
      },
      {
        id: 'nutri-color',
        name: 'Nutri Color',
        description: 'Nourishing color treatment',
      },
      {
        id: 'blonde',
        name: 'Blonde Expert',
        description: 'Blonde specialist line',
      },
    ],
  },

  // Colombian Brands
  {
    id: 'revlon-colombia',
    name: 'Revlon Professional Colombia',
    country: 'Colombia',
    description: 'Colombian division of Revlon Professional',
    lines: [
      {
        id: 'revlonissimo',
        name: 'Revlonissimo Colombia',
        description: 'Adapted for Latin hair',
      },
      {
        id: 'young-color',
        name: 'Young Color',
        description: 'Ammonia-free option',
      },
    ],
  },

  // Chilean Brands
  {
    id: 'saloon-in-chile',
    name: 'Saloon In',
    country: 'Chile',
    description: 'Chilean professional hair color',
    lines: [
      {
        id: 'color-cream',
        name: 'Color Cream',
        description: 'Creamy permanent color',
      },
      {
        id: 'lightening',
        name: 'Lightening System',
        description: 'Professional bleaching',
      },
      { id: 'toner', name: 'Toner Collection', description: 'Toning products' },
    ],
  },

  // Additional Asian Brands
  {
    id: 'napla',
    name: 'Napla',
    country: 'Japan',
    description: 'Japanese hair color innovation',
    lines: [
      {
        id: 'caretect',
        name: 'Caretect',
        description: 'Care-focused color system',
      },
      {
        id: 'n-color',
        name: 'N. Color',
        description: 'Natural color collection',
      },
      { id: 'bleach', name: 'Bleach Powder', description: 'Gentle lightening' },
    ],
  },
  {
    id: 'hoyu',
    name: 'Hoyu Professional',
    country: 'Japan',
    description: 'Japanese color technology leader',
    lines: [
      {
        id: 'promaster',
        name: 'Promaster Color Care',
        description: 'Professional color system',
      },
      { id: 'somarca', name: 'Somarca', description: 'Fashion color line' },
      {
        id: 'glamage',
        name: 'Glamage',
        description: 'Premium color collection',
      },
    ],
  },

  // African Brands
  {
    id: 'dark-and-lovely',
    name: 'Dark & Lovely Professional',
    country: 'South Africa',
    description: 'Professional products for textured hair',
    lines: [
      {
        id: 'fade-resist',
        name: 'Fade Resist',
        description: 'Long-lasting color for textured hair',
      },
      {
        id: 'go-intense',
        name: 'Go Intense',
        description: 'Ultra vibrant colors',
      },
      {
        id: 'precision',
        name: 'Precision Color',
        description: 'Precise color application',
      },
    ],
  },
  {
    id: 'ors',
    name: 'ORS Professional',
    country: 'South Africa',
    description: 'Olive oil-based professional color',
    lines: [
      {
        id: 'olive-oil',
        name: 'Olive Oil Color',
        description: 'Nourishing color system',
      },
      {
        id: 'professional',
        name: 'Professional Line',
        description: 'Salon-grade products',
      },
    ],
  },

  // Additional European Brands
  {
    id: 'alter-ego',
    name: 'Alter Ego Italy',
    country: 'Italy',
    description: 'Italian professional excellence',
    lines: [
      {
        id: 'technofruit',
        name: 'TechnoFruit Color',
        description: 'Fruit acid technology',
      },
      {
        id: 'blondego',
        name: 'BlondEgo',
        description: 'Blonde specialist range',
      },
      {
        id: 'color-ego',
        name: 'ColorEgo',
        description: 'Permanent color system',
      },
    ],
  },
  {
    id: 'be-hair',
    name: 'Be Hair',
    country: 'Italy',
    description: 'Italian sustainable hair color',
    lines: [
      {
        id: 'be-color',
        name: 'Be Color',
        description: 'Eco-friendly permanent color',
      },
      { id: '12-minute', name: '12 Minute', description: 'Fast-acting color' },
      { id: 'plex', name: 'Plex System', description: 'Bond-building color' },
    ],
  },

  // Turkish Brands
  {
    id: 'maxx-deluxe',
    name: 'Maxx Deluxe',
    country: 'Turkey',
    description: 'Turkish professional hair color',
    lines: [
      {
        id: 'premium',
        name: 'Premium Color',
        description: 'High-quality permanent color',
      },
      {
        id: 'lightening',
        name: 'Lightening Powder',
        description: 'Professional bleaching',
      },
      { id: 'toner', name: 'Toner Series', description: 'Toning collection' },
    ],
  },

  // Indian Brands
  {
    id: 'streax',
    name: 'Streax Professional',
    country: 'India',
    description: 'Indian professional hair color',
    lines: [
      {
        id: 'professional',
        name: 'Professional Color',
        description: 'Salon-grade color',
      },
      {
        id: 'insta-shine',
        name: 'Insta Shine',
        description: 'Instant shine color',
      },
      {
        id: 'hair-serum',
        name: 'Color Serum',
        description: 'Serum-based color',
      },
    ],
  },
];

export const getLinesByBrandId = (brandId: string): ProductLine[] => {
  const brand = professionalHairColorBrands.find(b => b.id === brandId);
  return brand ? brand.lines : [];
};

export const getBrandById = (brandId: string): Brand | undefined => {
  return professionalHairColorBrands.find(b => b.id === brandId);
};

export const searchBrands = (query: string): Brand[] => {
  if (!query.trim()) return professionalHairColorBrands;

  const lowercaseQuery = query.toLowerCase();
  return professionalHairColorBrands.filter(
    brand =>
      brand.name.toLowerCase().includes(lowercaseQuery) ||
      brand.country.toLowerCase().includes(lowercaseQuery) ||
      brand.lines.some(line => line.name.toLowerCase().includes(lowercaseQuery))
  );
};

export const getBrandsByCountry = (country: string): Brand[] => {
  return professionalHairColorBrands.filter(brand => brand.country === country);
};

export const getAllCountries = (): string[] => {
  const countries = [...new Set(professionalHairColorBrands.map(brand => brand.country))];
  return countries.sort();
};
