/**
 * BASIC VALIDATOR - Validaciones críticas simples y rápidas
 * 
 * Máximo 1 segundo de procesamiento, sin dependencias complejas
 * Solo validaciones que pueden prevenir errores graves
 */

interface BasicDiagnosis {
  current_level?: number;
  has_artificial_color?: boolean;
  hair_condition?: string;
  natural_base?: number;
}

interface BasicFormula {
  target_level?: number;
  process_type?: string;
  lightener?: boolean;
  oxidant_volume?: number;
  pre_pigmentation?: boolean;
  products?: Array<{ type?: string; brand?: string }>;
}

/**
 * Validador principal - MÁXIMO 1 segundo
 */
export function validateBasicRules(
  formula: BasicFormula,
  diagnosis: BasicDiagnosis,
  brand?: string
): string[] {
  const startTime = Date.now();
  const warnings: string[] = [];
  const MAX_PROCESSING_TIME = 1000; // 1 segundo límite estricto

  try {
    // Timeout protection - si toma mucho tiempo, salir
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 1. "Color no levanta color" - CRÍTICO
    const colorWarning = checkColorLiftingRule(formula, diagnosis);
    if (colorWarning) warnings.push(colorWarning);

    // Timeout check
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 2. Volumen de oxidante básico
    const oxidantWarning = checkOxidantVolume(formula, diagnosis);
    if (oxidantWarning) warnings.push(oxidantWarning);

    // Timeout check
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 3. Pre-pigmentación necesaria
    const prePigWarning = checkPrePigmentation(formula, diagnosis);
    if (prePigWarning) warnings.push(prePigWarning);

    // Timeout check
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 4. Proceso peligroso
    const dangerWarning = checkDangerousProcess(formula, diagnosis);
    if (dangerWarning) warnings.push(dangerWarning);

    // Timeout check
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 5. Productos básicos faltantes
    const productWarning = checkMissingProducts(formula);
    if (productWarning) warnings.push(productWarning);

  } catch (error) {
    // En caso de error, devolver warnings actuales sin fallar
    console.warn('Basic validator error:', error);
  }

  return warnings;
}

/**
 * 1. Validación crítica: Color no levanta color
 */
function checkColorLiftingRule(formula: BasicFormula, diagnosis: BasicDiagnosis): string | null {
  const hasArtificialColor = diagnosis.has_artificial_color || false;
  const currentLevel = diagnosis.current_level || 0;
  const targetLevel = formula.target_level || 0;
  const levelDifference = targetLevel - currentLevel;

  // Si tiene color artificial y quiere aclarar 3+ niveles
  if (hasArtificialColor && levelDifference >= 3) {
    return "⚠️ CRÍTICO: Color artificial no se puede aclarar directamente. Necesitas decoloración previa.";
  }

  return null;
}

/**
 * 2. Validación de volumen de oxidante
 */
function checkOxidantVolume(formula: BasicFormula, diagnosis: BasicDiagnosis): string | null {
  const oxidantVolume = formula.oxidant_volume;
  const currentLevel = diagnosis.current_level || 0;
  const targetLevel = formula.target_level || 0;
  const levelDifference = targetLevel - currentLevel;

  if (!oxidantVolume) return null;

  // Reglas básicas de oxidante
  if (levelDifference <= 0 && oxidantVolume > 10) {
    return "⚠️ Para depositar color usa 10 vol. Volúmenes altos pueden dañar sin aclarar.";
  }

  if (levelDifference >= 1 && levelDifference <= 2 && oxidantVolume !== 20) {
    return "⚠️ Para aclarar 1-2 niveles usa 20 vol para mejores resultados.";
  }

  if (levelDifference >= 3 && oxidantVolume < 30) {
    return "⚠️ Para aclarar 3+ niveles necesitas 30 vol o decoloración.";
  }

  return null;
}

/**
 * 3. Validación de pre-pigmentación
 */
function checkPrePigmentation(formula: BasicFormula, diagnosis: BasicDiagnosis): string | null {
  const currentLevel = diagnosis.current_level || 0;
  const targetLevel = formula.target_level || 0;
  const levelDifference = targetLevel - currentLevel;
  const needsPrePig = formula.pre_pigmentation;

  // Si oscurece 3+ niveles y no tiene pre-pigmentación
  if (levelDifference <= -3 && !needsPrePig) {
    return "⚠️ Oscurecer 3+ niveles requiere pre-pigmentación para evitar que se deslave.";
  }

  return null;
}

/**
 * 4. Validación de procesos peligrosos
 */
function checkDangerousProcess(formula: BasicFormula, diagnosis: BasicDiagnosis): string | null {
  const currentLevel = diagnosis.current_level || 0;
  const targetLevel = formula.target_level || 0;
  const levelDifference = targetLevel - currentLevel;
  const hairCondition = diagnosis.hair_condition?.toLowerCase();

  // Aclarar 6+ niveles en una sesión
  if (levelDifference >= 6) {
    return "🚨 PELIGROSO: Aclarar 6+ niveles en una sesión puede causar rotura severa. Considera proceso en etapas.";
  }

  // Cabello dañado + proceso fuerte
  if ((hairCondition?.includes('dañado') || hairCondition?.includes('seco')) && levelDifference >= 4) {
    return "🚨 PELIGROSO: Cabello dañado no debe aclarar 4+ niveles. Riesgo alto de rotura.";
  }

  return null;
}

/**
 * 5. Validación de productos faltantes
 */
function checkMissingProducts(formula: BasicFormula): string | null {
  const products = formula.products || [];
  const needsLightener = formula.lightener || formula.target_level && formula.target_level >= 9;
  
  // Si necesita decolorar pero no hay decolorante
  if (needsLightener) {
    const hasLightener = products.some(p => 
      p.type?.toLowerCase().includes('decolor') || 
      p.type?.toLowerCase().includes('lightener') ||
      p.type?.toLowerCase().includes('polvo')
    );
    
    if (!hasLightener) {
      return "⚠️ Falta decolorante para alcanzar el nivel objetivo. Añade polvo decolorante.";
    }
  }

  return null;
}

/**
 * Función auxiliar: Verificar si el proceso es seguro
 */
export function isProcessSafe(currentLevel: number, targetLevel: number): { safe: boolean; reason?: string } {
  const levelDifference = targetLevel - currentLevel;
  
  // Proceso extremo
  if (levelDifference >= 6) {
    return { 
      safe: false, 
      reason: "Diferencia de 6+ niveles es peligrosa en una sesión" 
    };
  }
  
  // Proceso normal
  if (Math.abs(levelDifference) <= 3) {
    return { safe: true };
  }
  
  // Proceso intermedio - precaución
  return { 
    safe: true, 
    reason: "Proceso fuerte - evaluar condición del cabello" 
  };
}

/**
 * Función auxiliar: Verificar si necesita remoción de color
 */
export function needsColorRemoval(diagnosis: BasicDiagnosis, targetLevel: number): boolean {
  const hasArtificialColor = diagnosis.has_artificial_color || false;
  const currentLevel = diagnosis.current_level || 0;
  const levelDifference = targetLevel - currentLevel;
  
  // Si tiene color artificial y quiere aclarar 2+ niveles
  return hasArtificialColor && levelDifference >= 2;
}

/**
 * Función de timeout - procesar con límite de tiempo estricto
 */
export function validateWithTimeout(
  formula: BasicFormula,
  diagnosis: BasicDiagnosis,
  brand?: string,
  timeoutMs: number = 1000
): Promise<string[]> {
  return Promise.race([
    Promise.resolve(validateBasicRules(formula, diagnosis, brand)),
    new Promise<string[]>((_, reject) => {
      setTimeout(() => reject(new Error('Validation timeout')), timeoutMs);
    })
  ]).catch(() => {
    // Si hay timeout, devolver array vacío
    return [];
  });
}