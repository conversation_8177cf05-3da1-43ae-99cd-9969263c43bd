import React, { useState, useMemo } from 'react';
import { logger } from '@/utils/logger';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import {
  ChevronDown,
  ChevronUp,
  Package,
  Copy,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
} from 'lucide-react-native';
import * as Clipboard from 'expo-clipboard';
import { router } from 'expo-router';
import Colors from '@/constants/colors';
import { Formulation } from '@/types/formulation';
import { useInventoryStore } from '@/stores/inventory-store';
import { parseFormulaTextToProducts } from '@/utils/parseFormula';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth-store';
import { Product } from '@/types/inventory';

interface ProductMatch {
  product: Product;
  matchScore: number;
  matchType: 'exact' | 'partial' | 'fuzzy';
  confidence: number;
}

interface MaterialsSummaryCardProps {
  formulationData: Formulation | null;
  formulaText: string;
  selectedBrand: string;
  selectedLine: string;
}

interface MaterialItem {
  productName: string;
  totalQuantity: number;
  unit: string;
  inStock?: boolean;
  stockStatus?: 'available' | 'low' | 'out'; // New field for 3-state status
  matchedProduct?: {
    id: string;
    name: string;
    displayName?: string;
    confidence: number;
    currentStock?: number;
    unitType?: string;
  };
  matchType?: 'exact' | 'partial' | 'fuzzy' | 'none';
}

export const MaterialsSummaryCard: React.FC<MaterialsSummaryCardProps> = ({
  formulationData,
  formulaText,
  selectedBrand,
  selectedLine,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const { saveProductMapping, incrementMappingUsage } = useInventoryStore();
  const { currentUser } = useAuthStore();
  const [pendingConfirmations, setPendingConfirmations] = useState<{
    [key: string]: boolean;
  }>({});

  // Extract materials from structured data or parse from text
  const materials = useMemo<MaterialItem[]>(() => {
    const materialMap = new Map<string, MaterialItem>();

    // Return empty array if no formula text
    if (!formulaText) return [];

    if (formulationData && formulationData.steps) {
      // Use structured data
      formulationData.steps.forEach(step => {
        if (step.mix) {
          step.mix.forEach(product => {
            const key = product.productName;
            const existing = materialMap.get(key);

            if (existing) {
              existing.totalQuantity += product.quantity;
            } else {
              materialMap.set(key, {
                productName: product.productName,
                totalQuantity: product.quantity,
                unit: product.unit,
              });
            }
          });
        }
      });
    } else if (formulaText) {
      // Use the unified parser
      const products = parseFormulaTextToProducts(formulaText);

      products.forEach(product => {
        const key = product.name;
        const existing = materialMap.get(key);

        if (existing) {
          existing.totalQuantity += product.amount;
        } else {
          materialMap.set(key, {
            productName: product.name,
            totalQuantity: product.amount,
            unit: product.unit,
          });
        }
      });
    }

    // Check stock availability and match products
    const materialsArray = Array.from(materialMap.values());
    materialsArray.forEach(material => {
      // Try structured matching first if we have the data
      let matches: ProductMatch[] = [];

      if (formulationData && formulationData.steps) {
        // Extract structured data from the material
        const structuredData = formulationData.steps
          .flatMap(step => step.mix || [])
          .find(p => p.productName === material.productName);

        if (structuredData) {
          matches = InventoryConsumptionService.findMatchingProductsStructured({
            brand: structuredData.brand,
            line: structuredData.line,
            type: structuredData.type,
            shade: structuredData.shade,
            name: material.productName,
          });
        }
      }

      // Fallback to name-based matching
      if (matches.length === 0) {
        matches = InventoryConsumptionService.findMatchingProducts(material.productName);
      }

      const bestMatch = matches[0];

      if (bestMatch && bestMatch.matchScore > 60) {
        const product = bestMatch.product;
        material.inStock = product.currentStock >= material.totalQuantity;

        // Determine 3-state stock status
        if (product.currentStock >= material.totalQuantity) {
          material.stockStatus = 'available';
        } else if (product.currentStock > 0) {
          material.stockStatus = 'low';
        } else {
          material.stockStatus = 'out';
        }

        material.matchedProduct = {
          id: product.id,
          name: product.name,
          displayName: product.displayName,
          confidence: bestMatch.confidence || bestMatch.matchScore,
          currentStock: product.currentStock,
          unitType: product.unitType,
        };
        material.matchType = bestMatch.matchType;
      } else {
        material.inStock = false;
        material.stockStatus = 'out';
        material.matchType = 'none';
      }
    });

    return materialsArray;
  }, [formulationData, formulaText]);

  const copyToClipboard = async () => {
    const text = materials.map(m => `• ${m.productName}: ${m.totalQuantity}${m.unit}`).join('\n');

    const fullText = `Lista de Materiales - ${selectedBrand} ${selectedLine}\n\n${text}`;

    await Clipboard.setStringAsync(fullText);
    Alert.alert('Copiado', 'Lista de materiales copiada al portapapeles');
  };

  const navigateToInventory = () => {
    router.push('/inventory');
  };

  const totalItems = materials.length;
  const itemsInStock = materials.filter(m => m.inStock).length;
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefreshStock = async () => {
    setIsRefreshing(true);
    // Force re-render by updating a dummy state
    // The useMemo will re-calculate stock availability
    await new Promise(resolve => setTimeout(resolve, 500));
    setIsRefreshing(false);
  };

  const handleConfirmMatch = async (
    iaProductName: string,
    matchedProduct: MaterialItem['matchedProduct']
  ) => {
    if (!currentUser?.salon_id) return;

    // Marcar como confirmado en UI
    setPendingConfirmations(prev => ({ ...prev, [iaProductName]: true }));

    // Guardar mapping localmente
    saveProductMapping(iaProductName, matchedProduct.id, matchedProduct.confidence);

    // Sincronizar con Supabase
    try {
      await supabase.from('product_mappings').upsert(
        {
          salon_id: currentUser.salon_id,
          ai_product_name: iaProductName,
          inventory_product_id: matchedProduct.id,
          confidence: Math.min(matchedProduct.confidence + 5, 100), // Aumentar confianza por confirmación
          usage_count: 1,
        },
        {
          onConflict: 'salon_id,ai_product_name',
        }
      );

      // Incrementar uso si ya existía
      incrementMappingUsage(iaProductName);

      Alert.alert(
        'Producto confirmado',
        'El producto se ha asociado correctamente. La próxima vez se reconocerá automáticamente.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      logger.error('Error saving product mapping:', error);
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
        activeOpacity={0.7}
      >
        <View style={styles.headerLeft}>
          <Package size={20} color={Colors.light.primary} />
          <View style={styles.headerText}>
            <Text style={styles.title}>Lista de Compra</Text>
            <Text style={styles.subtitle}>
              {totalItems} productos • {itemsInStock}/{totalItems} en stock
            </Text>
          </View>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity
            onPress={handleRefreshStock}
            disabled={isRefreshing}
            style={styles.refreshButton}
          >
            <RefreshCw
              size={18}
              color={isRefreshing ? Colors.light.gray : Colors.light.primary}
              style={isRefreshing ? styles.rotating : undefined}
            />
          </TouchableOpacity>
          {isExpanded ? (
            <ChevronUp size={20} color={Colors.light.gray} />
          ) : (
            <ChevronDown size={20} color={Colors.light.gray} />
          )}
        </View>
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.content}>
          <View style={styles.materialsList}>
            {materials.map((material, index) => (
              <View
                key={index}
                style={[styles.materialItem, index % 2 === 0 && styles.materialItemEven]}
              >
                <View style={styles.materialItemContent}>
                  <View style={styles.materialProductInfo}>
                    <Text
                      style={[
                        styles.materialName,
                        !material.matchedProduct && styles.materialNameMissing,
                      ]}
                    >
                      {material.productName}
                    </Text>
                    <Text style={styles.materialQuantity}>
                      {material.totalQuantity}
                      {material.unit}
                    </Text>

                    {/* Para productos de coloración, solo mostrar info si NO es match exacto */}
                    {material.matchedProduct &&
                      material.matchedProduct.confidence < 100 &&
                      !pendingConfirmations[material.productName] && (
                        <View style={styles.matchInfo}>
                          {/* Detectar si es producto de coloración */}
                          {material.productName.toLowerCase().includes('color') ||
                          material.productName.toLowerCase().includes('tinte') ||
                          material.productName.toLowerCase().includes('illumina') ||
                          material.productName.toLowerCase().includes('koleston') ||
                          material.productName.toLowerCase().includes('majirel') ? (
                            <>
                              <Text style={styles.matchInfoText}>
                                En inventario:{' '}
                                {material.matchedProduct.displayName ||
                                  material.matchedProduct.name}
                              </Text>
                              <Text style={styles.matchWarning}>
                                ⚠️ Tono diferente - No es el producto exacto
                              </Text>
                            </>
                          ) : (
                            <>
                              <Text style={styles.matchInfoText}>
                                Identificado como:{' '}
                                {material.matchedProduct.displayName ||
                                  material.matchedProduct.name}
                              </Text>
                              <Text style={styles.matchConfidence}>
                                Confianza: {Math.round(material.matchedProduct.confidence)}%
                              </Text>
                            </>
                          )}
                        </View>
                      )}
                  </View>

                  <View style={styles.materialStatusContainer}>
                    {material.matchedProduct ? (
                      <>
                        {/* Mostrar botón de confirmar SOLO para productos NO-coloración */}
                        {material.matchedProduct.confidence < 100 &&
                          !pendingConfirmations[material.productName] &&
                          !(
                            material.productName.toLowerCase().includes('color') ||
                            material.productName.toLowerCase().includes('tinte') ||
                            material.productName.toLowerCase().includes('illumina') ||
                            material.productName.toLowerCase().includes('koleston') ||
                            material.productName.toLowerCase().includes('majirel')
                          ) && (
                            <TouchableOpacity
                              style={styles.confirmButton}
                              onPress={() =>
                                handleConfirmMatch(material.productName, material.matchedProduct)
                              }
                            >
                              <CheckCircle size={14} color={Colors.light.primary} />
                              <Text style={styles.confirmButtonText}>Confirmar</Text>
                            </TouchableOpacity>
                          )}

                        {/* Badge de stock - 3 estados */}
                        {material.stockStatus === 'available' ? (
                          <View style={[styles.materialBadge, styles.materialBadgeSuccess]}>
                            <CheckCircle size={16} color={Colors.light.success} />
                            <Text style={styles.materialBadgeTextSuccess}>Disponible</Text>
                          </View>
                        ) : material.stockStatus === 'low' ? (
                          <View style={[styles.materialBadge, styles.materialBadgeWarning]}>
                            <AlertTriangle size={16} color={Colors.light.warning} />
                            <Text style={styles.materialBadgeTextWarning}>Stock bajo</Text>
                          </View>
                        ) : (
                          <View style={[styles.materialBadge, styles.materialBadgeError]}>
                            <XCircle size={16} color={Colors.light.error} />
                            <Text style={styles.materialBadgeTextError}>No stock</Text>
                          </View>
                        )}
                      </>
                    ) : (
                      <View style={[styles.materialBadge, styles.materialBadgeError]}>
                        <XCircle size={16} color={Colors.light.error} />
                        <Text style={styles.materialBadgeTextError}>No encontrado</Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            ))}
          </View>

          {itemsInStock < totalItems && (
            <View style={styles.warningContainer}>
              <AlertTriangle size={16} color={Colors.light.warning} />
              <Text style={styles.warningText}>
                Se descontarán solo los productos encontrados en inventario ({itemsInStock} de{' '}
                {totalItems})
              </Text>
            </View>
          )}

          <TouchableOpacity style={styles.copyButton} onPress={copyToClipboard}>
            <Copy size={16} color={Colors.light.primary} />
            <Text style={styles.copyButtonText}>Copiar lista</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.inventoryButton} onPress={navigateToInventory}>
            <Package size={16} color={Colors.light.primary} />
            <Text style={styles.inventoryButtonText}>Ver inventario completo</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerText: {
    marginLeft: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  subtitle: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 2,
  },
  content: {
    padding: 16,
    paddingTop: 0,
  },
  materialsList: {
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  materialItem: {
    backgroundColor: Colors.light.background,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + '20',
  },
  materialItemEven: {
    backgroundColor: Colors.light.surface + '50',
  },
  materialItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  materialProductInfo: {
    flex: 1,
    marginRight: 12,
  },
  materialName: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 2,
  },
  materialNameMissing: {
    color: Colors.light.gray,
  },
  materialQuantity: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.gray,
  },
  materialStatusContainer: {
    flexShrink: 0,
  },
  materialBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  materialBadgeSuccess: {
    backgroundColor: Colors.light.success + '15',
  },
  materialBadgeWarning: {
    backgroundColor: Colors.light.warning + '15',
  },
  materialBadgeError: {
    backgroundColor: Colors.light.error + '15',
  },
  materialBadgeTextSuccess: {
    fontSize: 12,
    color: Colors.light.success,
    fontWeight: '600',
  },
  materialBadgeTextWarning: {
    fontSize: 12,
    color: Colors.light.warning,
    fontWeight: '600',
  },
  materialBadgeTextError: {
    fontSize: 12,
    color: Colors.light.error,
    fontWeight: '600',
  },
  warningContainer: {
    backgroundColor: Colors.light.warning + '10',
    borderRadius: 8,
    padding: 12,
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  warningText: {
    fontSize: 12,
    color: Colors.light.warning,
    flex: 1,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 12,
    gap: 8,
  },
  copyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  inventoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.primary,
    gap: 8,
  },
  inventoryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  refreshButton: {
    padding: 4,
  },
  rotating: {
    transform: [{ rotate: '360deg' }],
  },
  matchInfo: {
    marginTop: 4,
    paddingTop: 4,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border + '30',
  },
  matchInfoText: {
    fontSize: 12,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },
  matchConfidence: {
    fontSize: 11,
    color: Colors.light.gray + '80',
    marginTop: 2,
  },
  matchWarning: {
    fontSize: 12,
    color: Colors.light.warning,
    fontWeight: '600',
    marginTop: 2,
  },
  confirmButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + '10',
    gap: 4,
    marginRight: 8,
  },
  confirmButtonText: {
    fontSize: 12,
    color: Colors.light.primary,
    fontWeight: '600',
  },
});
